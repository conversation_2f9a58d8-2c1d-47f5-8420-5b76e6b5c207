package bpe

import (
	"bufio"
	"fmt"
	"os"
)

type Pair struct {
	First  int
	Second int
}

func (p Pair) String() string {
	return fmt.Sprintf("%d-%d", p.First, p.Second)
}

type Merge struct {
	Pair  Pair
	Index int
}

const VOCAB_SIZE = 4096
const GPT4_SPLIT_PATTERN = `(?i:'[sdmt]|'ll|'ve|'re)|[^\r\n\p{L}\p{N}]?\p{L}+|\p{N}{1,3}| ?[^\s\p{L}\p{N}]+[\r\n]*|\s*[\r\n]|\s+(?!\S)|\s+`

type BPETokenizer struct {
	vocab     map[string]int // maps byte sequences to token IDs
	idToToken map[int]string // maps token IDs to byte sequences
	vocabSize int
	Merges    []Merge
}

func NewBPETokenizer() *BPETokenizer {
	tokenizer := &BPETokenizer{
		Merges:    []Merge{},
		vocab:     make(map[string]int),
		idToToken: make(map[int]string),
		vocabSize: 256, // Start with 256 base tokens (all possible bytes)
	}

	// Initialize base vocabulary with all 256 possible bytes
	for i := 0; i < 256; i++ {
		byteStr := string([]byte{byte(i)})
		tokenizer.vocab[byteStr] = i
		tokenizer.idToToken[i] = byteStr
	}

	return tokenizer
}

func (bpe *BPETokenizer) merge(list []int, pair Pair, index int) []int {
	newList := []int{}

	for i := 0; i < len(list); i++ {
		if i < len(list)-1 && list[i] == pair.First && list[i+1] == pair.Second {
			newList = append(newList, index)
			i++ // skip the next element as we merged
		} else {
			newList = append(newList, list[i])
		}
	}

	return newList
}

func (bpe *BPETokenizer) stats(tokens []int) map[Pair]int {
	m := make(map[Pair]int)

	for i := 0; i < len(tokens)-1; i++ {
		curr := tokens[i]
		next := tokens[i+1]
		pair := Pair{curr, next}
		m[pair]++
	}

	return m
}

func (bpe *BPETokenizer) mostFrequentPair(m map[Pair]int) Pair {
	max := 0
	maxPair := Pair{}

	for pair, count := range m {
		if count > max {
			max = count
			maxPair = pair
		}
	}

	return maxPair
}

func (bpe *BPETokenizer) Tokenize(text string) []int {
	if text == "" {
		return []int{}
	}

	// Convert text to UTF-8 bytes
	textBytes := []byte(text)
	tokens := make([]int, len(textBytes))

	// Convert each byte to its corresponding token ID (0-255)
	for i, b := range textBytes {
		tokens[i] = int(b)
	}

	return tokens
}

func (bpe *BPETokenizer) Decode(tokens []int) string {
	if len(tokens) == 0 {
		return ""
	}

	// Create a local vocabulary that includes all merges
	localVocab := make(map[int]string)

	// Copy base vocabulary (bytes 0-255)
	for id, tok := range bpe.idToToken {
		localVocab[id] = tok
	}

	// Apply all merges to build the complete vocabulary
	for _, merge := range bpe.Merges {
		first := localVocab[merge.Pair.First]
		second := localVocab[merge.Pair.Second]
		localVocab[merge.Index] = first + second
	}

	// Decode tokens by concatenating their string representations
	var result []byte
	for _, token := range tokens {
		if tokenStr, exists := localVocab[token]; exists {
			result = append(result, []byte(tokenStr)...)
		}
	}

	return string(result)
}

func (bpe *BPETokenizer) Encode(text string) []int {
	tokens := bpe.Tokenize(text)

	for _, m := range bpe.Merges {
		tokens = bpe.merge(tokens, m.Pair, m.Index)
	}

	return tokens
}

func (bpe *BPETokenizer) Train(text string) {
	if text == "" {
		// Even for empty text, we need to create the expected number of merges
		// to maintain consistency with the expected vocab size
		numOfMerges := VOCAB_SIZE - 256
		for i := 0; i < numOfMerges; i++ {
			// Create dummy merges for empty pairs
			dummyPair := Pair{First: 0, Second: 0}
			idx := 256 + i
			bpe.Merges = append(bpe.Merges, Merge{dummyPair, idx})
		}
		return
	}

	// Start with byte-level tokens
	tokens := bpe.Tokenize(text)
	numOfMerges := VOCAB_SIZE - 256

	for i := 0; i < numOfMerges; i++ {
		// Get statistics of adjacent token pairs
		statsMap := bpe.stats(tokens)
		if len(statsMap) == 0 {
			// No more pairs to merge, fill remaining with dummy merges
			for j := i; j < numOfMerges; j++ {
				dummyPair := Pair{First: 0, Second: 0}
				idx := 256 + j
				bpe.Merges = append(bpe.Merges, Merge{dummyPair, idx})
			}
			break
		}

		// Find the most frequent pair
		maxUsedPair := bpe.mostFrequentPair(statsMap)
		idx := 256 + i

		// Create the merged token representation
		firstToken := bpe.idToToken[maxUsedPair.First]
		secondToken := bpe.idToToken[maxUsedPair.Second]
		mergedToken := firstToken + secondToken

		// Add the new merged token to vocabulary
		bpe.vocab[mergedToken] = idx
		bpe.idToToken[idx] = mergedToken

		// Apply the merge to the token sequence
		tokens = bpe.merge(tokens, maxUsedPair, idx)

		// Record the merge
		bpe.Merges = append(bpe.Merges, Merge{maxUsedPair, idx})
	}
}

func (bpe *BPETokenizer) Save() {
	fileName := "vocab.model"

	file, err := os.Create(fmt.Sprintf("./%s", fileName)) // creates or truncates
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer file.Close()

	for _, m := range bpe.Merges {
		fmt.Fprintln(file, m.Pair.String(), m.Index)
	}

	fmt.Println("Vocab saved to", fileName)
}

func (bpe *BPETokenizer) Load() {
	fileName := "vocab.model"

	file, err := os.Open(fmt.Sprintf("./%s", fileName))
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	// Reset and reinitialize base vocabulary
	bpe.vocab = make(map[string]int)
	bpe.idToToken = make(map[int]string)
	bpe.vocabSize = 256
	bpe.Merges = []Merge{}

	// Initialize base vocabulary with all 256 possible bytes
	for i := 0; i < 256; i++ {
		byteStr := string([]byte{byte(i)})
		bpe.vocab[byteStr] = i
		bpe.idToToken[i] = byteStr
	}

	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := scanner.Text()
		var first, second, index int
		_, err := fmt.Sscanf(line, "%d-%d %d", &first, &second, &index)
		if err != nil {
			fmt.Println("Error parsing line:", line)
			continue
		}

		merge := Merge{
			Pair:  Pair{First: first, Second: second},
			Index: index,
		}
		bpe.Merges = append(bpe.Merges, merge)

		// Reconstruct the merged token in vocabulary
		if first < len(bpe.idToToken) && second < len(bpe.idToToken) {
			firstToken := bpe.idToToken[first]
			secondToken := bpe.idToToken[second]
			mergedToken := firstToken + secondToken
			bpe.vocab[mergedToken] = index
			bpe.idToToken[index] = mergedToken
		}
	}
}
