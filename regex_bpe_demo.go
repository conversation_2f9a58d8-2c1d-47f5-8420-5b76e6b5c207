package main

import (
	"bpicori/bpe-tokenizer/bpe"
	"fmt"
	"github.com/dlclark/regexp2"
)

func main() {
	fmt.Println("=== Regex + BPE Tokenizer Demo ===")
	
	// Show how the regex splits text first
	text := "Hello, world! I'm testing 123 tokens."
	fmt.Printf("Original text: %q\n", text)
	
	// Show regex chunks
	fmt.Println("\n=== Regex Splitting ===")
	const GPT4_SPLIT_PATTERN = `(?i:'[sdmt]|'ll|'ve|'re)|[^\r\n\p{L}\p{N}]?\p{L}+|\p{N}{1,3}| ?[^\s\p{L}\p{N}]+[\r\n]*|\s*[\r\n]|\s+(?!\S)|\s+`
	re := regexp2.MustCompile(GPT4_SPLIT_PATTERN, regexp2.None)
	
	start := 0
	chunkIndex := 0
	for start < len(text) {
		match, err := re.FindStringMatch(text[start:])
		if err != nil || match == nil {
			break
		}
		matched := match.String()
		fmt.Printf("Chunk %d: %q (bytes: %v)\n", chunkIndex, matched, []byte(matched))
		start += match.Index + len(matched)
		chunkIndex++
	}
	
	// Create tokenizer and show byte-level tokenization
	fmt.Println("\n=== Byte-Level Tokenization ===")
	tokenizer := bpe.NewBPETokenizer()
	tokens := tokenizer.Tokenize(text)
	fmt.Printf("Byte tokens: %v\n", tokens)
	fmt.Printf("Number of byte tokens: %d\n", len(tokens))
	
	// Train the tokenizer
	fmt.Println("\n=== Training BPE ===")
	trainingText := "Hello, world! Hello, there! I'm testing tokens. Testing, testing, 123."
	tokenizer.Train(trainingText)
	fmt.Printf("Trained on: %q\n", trainingText)
	fmt.Printf("Number of merges learned: %d\n", len(tokenizer.Merges))
	
	// Show some meaningful merges (skip dummy ones)
	fmt.Println("Some learned merges:")
	count := 0
	for i, merge := range tokenizer.Merges {
		if merge.Pair.First != 0 || merge.Pair.Second != 0 {
			if count < 10 { // Show first 10 real merges
				first := string([]byte{byte(merge.Pair.First)})
				second := string([]byte{byte(merge.Pair.Second)})
				if merge.Pair.First >= 256 {
					first = fmt.Sprintf("token_%d", merge.Pair.First)
				}
				if merge.Pair.Second >= 256 {
					second = fmt.Sprintf("token_%d", merge.Pair.Second)
				}
				fmt.Printf("  Merge %d: %s + %s -> token_%d\n", i, first, second, merge.Index)
				count++
			}
		}
	}
	
	// Encode with trained model
	fmt.Println("\n=== Encoding with BPE ===")
	encoded := tokenizer.Encode(text)
	fmt.Printf("Original text: %q\n", text)
	fmt.Printf("Encoded tokens: %v\n", encoded)
	fmt.Printf("Compression: %d bytes -> %d tokens\n", len(text), len(encoded))
	
	// Decode back
	decoded := tokenizer.Decode(encoded)
	fmt.Printf("Decoded text: %q\n", decoded)
	
	// Verify round-trip
	if decoded == text {
		fmt.Println("✅ Perfect round-trip!")
	} else {
		fmt.Println("❌ Round-trip failed!")
		fmt.Printf("Expected: %q\n", text)
		fmt.Printf("Got:      %q\n", decoded)
	}
	
	// Test with various text types
	fmt.Println("\n=== Testing Different Text Types ===")
	testTexts := []string{
		"simple",
		"Hello123",
		"don't",
		"I'm happy!",
		"testing... 456",
	}
	
	for _, testText := range testTexts {
		encoded := tokenizer.Encode(testText)
		decoded := tokenizer.Decode(encoded)
		status := "✅"
		if decoded != testText {
			status = "❌"
		}
		fmt.Printf("%s %q -> %v -> %q\n", status, testText, encoded, decoded)
	}
}
