package main

import (
	"bpicori/bpe-tokenizer/bpe"
	"fmt"
)

func main() {
	fmt.Println("=== BPE Tokenizer Demo ===")
	
	// Create a new tokenizer
	tokenizer := bpe.NewBPETokenizer()
	
	// Test text
	text := "hello world hello"
	fmt.Printf("Original text: %q\n", text)
	
	// Show byte-level tokenization (before training)
	tokens := tokenizer.Tokenize(text)
	fmt.Printf("Byte-level tokens: %v\n", tokens)
	fmt.Printf("Number of tokens: %d\n", len(tokens))
	
	// Show what each token represents
	fmt.Println("Token breakdown:")
	for i, token := range tokens {
		if token < 256 {
			fmt.Printf("  Token %d: byte %d = %q\n", i, token, string([]byte{byte(token)}))
		}
	}
	
	// Train the tokenizer
	fmt.Println("\n=== Training BPE ===")
	tokenizer.Train(text)
	fmt.Printf("Number of merges learned: %d\n", len(tokenizer.Merges))
	
	// Show some learned merges
	fmt.Println("First few merges:")
	for i, merge := range tokenizer.Merges[:min(5, len(tokenizer.Merges))] {
		if merge.Pair.First != 0 || merge.Pair.Second != 0 { // Skip dummy merges
			fmt.Printf("  Merge %d: tokens %d + %d -> %d\n", i, merge.Pair.First, merge.Pair.Second, merge.Index)
		}
	}
	
	// Encode with trained model
	encoded := tokenizer.Encode(text)
	fmt.Printf("\nEncoded tokens (after BPE): %v\n", encoded)
	fmt.Printf("Number of tokens after BPE: %d\n", len(encoded))
	
	// Decode back
	decoded := tokenizer.Decode(encoded)
	fmt.Printf("Decoded text: %q\n", decoded)
	
	// Verify round-trip
	if decoded == text {
		fmt.Println("✅ Round-trip successful!")
	} else {
		fmt.Println("❌ Round-trip failed!")
	}
	
	// Test with different text
	fmt.Println("\n=== Testing with new text ===")
	newText := "hello there"
	fmt.Printf("New text: %q\n", newText)
	
	newEncoded := tokenizer.Encode(newText)
	fmt.Printf("Encoded: %v\n", newEncoded)
	
	newDecoded := tokenizer.Decode(newEncoded)
	fmt.Printf("Decoded: %q\n", newDecoded)
	
	if newDecoded == newText {
		fmt.Println("✅ New text round-trip successful!")
	} else {
		fmt.Println("❌ New text round-trip failed!")
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
